<template>
  <div :class="chatClass">
    <div class="chatSection">
      <!-- 左侧智能体列表，仅在非移动端且有多于一个智能体时显示 -->
      <AgentList
        v-if="agentListVisible"
        :agentList="agentList"
        :currentAgent="currentAgent"
        @selectAgent="onSelectAgent"
      />

      <div class="chatApp" v-if="currentAgent?.chartType === 'chatBI-chat'">
        <SusChat
          ref="susChatRef"
          :currentClientAgent="currentAgent"
          :isDeveloper="isDeveloper"
          :chatVisible="chatVisible"
          :integrateSystem="integrateSystem"
          :historyVisible="historyVisible"
          @openAgents="onOpenAgents"
          @toggleHistoryVisible="onToggleHistoryVisible"
          @addConversation="onAddConversation"
          @reportMsgEvent="onReportMsgEvent"
        />
      </div>

      <div
        class="chatApp"
        v-if="
          currentAgent?.chartType === 'chat' ||
          currentAgent?.chartType === 'advanced-chat'
        "
      >
        <DifyChat
          ref="difyChatRef"
          :chartId="currentAgent?.chartId"
          :currentAgent="currentAgent"
          :conversationId="difyConversationId"
          @toggleHistoryVisible="onToggleHistoryVisible"
          @openAgents="onOpenAgents"
          @update:conversationId="onUpdateConversationId"
          :isPublic="isPublic"
        />
      </div>

      <!-- 右侧会话列表 -->
      <Conversation
        v-if="currentAgent?.chartType === 'chatBI-chat'"
        :currentAgent="currentAgent"
        :currentClientAgent="currentAgent"
        :currentConversation="currentConversation"
        :historyVisible="historyVisible"
        @selectConversation="onSelectConversation"
        @closeConversation="onCloseConversation"
        ref="conversationRef"
      />

      <DifyConversation
        v-if="
          (currentAgent?.chartType === 'chat' ||
            currentAgent?.chartType === 'advanced-chat') &&
          !isPublic
        "
        :currentAgent="currentAgent"
        :currentConversation="currentDifyConversation"
        :historyVisible="historyVisible"
        :currentConversationId="difyConversationId"
        :selectConversation="onDifySelectConversation"
        @closeConversation="onCloseConversation"
        ref="conversationRef"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  ref,
  computed,
  onMounted,
  onUnmounted,
  nextTick,
  watch,
} from "vue";
import { updateMessageContainerScroll, uuid, setToken } from "../utils/utils";
import Conversation from "./Conversation/index.vue";
import { cloneDeep, throttle } from "lodash";
import AgentList from "./AgentList/index.vue";
import {
  ConversationDetailType,
  MessageItem,
  MessageTypeEnum,
  AgentType,
} from "./type";
import {
  HistoryMsgItemType,
  MsgDataType,
  SendMsgParamsType,
} from "../common/type";
import { queryAgentList } from "./service";
import { getHistoryMsg } from "../service";

import classNames from "classnames";
import DifyChat from "@/DifyChat/index.vue";
import DifyConversation from "@/DifyChat/components/Conversation.vue";

import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

import type { ClientAgentType, DifyConversationItem } from "@/types/client";
import { setKKServerUrl, setKKLocalUrl, setISMICRO } from "@/config/config";

import { validateNull } from "@/utils/utils";

import SusChat from "@/Chat/SusChat.vue";

// 判断是否是移动设备
// export const isMobile = window.navigator.userAgent.match(
//   /(iPhone|iPod|Android|ios)/i
// );

// 设置日期本地化
dayjs.locale("zh-cn");

export default defineComponent({
  name: "Chat",
  components: {
    Conversation,
    AgentList,
    DifyChat,
    DifyConversation,
    SusChat,
  },
  props: {
    token: String, // 认证token
    agentIds: Array, // 智能体ID列表
    initialAgentId: Number, // 初始智能体ID
    chatVisible: Boolean, // 聊天界面是否可见
    noInput: Boolean, // 是否禁用输入
    isDeveloper: Boolean, // 是否是开发者模式
    integrateSystem: String, // 集成系统标识
    isCopilot: Boolean, // 是否是Copilot模式
    isPublic: Boolean, // 是否是公开模式
    kkServerUrl: String, // kkfile服务器URL
    kkLocalUrl: String, // kkfile本地URL
    isMicro: Boolean, // 是否是微服务模式
  },
  emits: ["currentAgentChange", "reportMsgEvent"],
  setup(props, { emit, expose }) {
    // 状态定义
    const historyVisible = ref(false); // 历史记录是否可见
    const agentList = ref<ClientAgentType[]>([]); // 智能体列表
    const currentAgent = ref<ClientAgentType>(); // 当前选中的智能体
    const agentListVisible = ref(true); // 智能体列表是否可见



    const currentConversation = ref<ConversationDetailType | undefined>(
      undefined
    ); // 当前会话
    const currentDifyConversation = ref<DifyConversationItem | undefined>(
      undefined
    );

    // 组件引用
    const conversationRef = ref();
    const difyChatRef = ref();
    const susChatRef = ref();

    // 计算属性：聊天界面类名
    const chatClass = computed(() => {
      return classNames("hr-chat-box", {
        // mobile: isMobile,
        historyVisible: historyVisible.value,
      });
    });

    const difyConversationId = ref<string>("");

    const onUpdateConversationId = (id: string) => {
      difyConversationId.value = id;
    };

    // 更新当前智能体
    const updateCurrentAgent = (agent?: ClientAgentType) => {
      // 将 AgentType 转换为 ClientAgentType
      if (agent) {
        const clientAgent: ClientAgentType = {
          ...agent,
          chartType: (agent.chartType || "chatBI-chat") as any,
        };
        currentAgent.value = clientAgent;
        emit("currentAgentChange", clientAgent);
      } else {
        currentAgent.value = undefined;
        emit("currentAgentChange", undefined);
      }
      localStorage.setItem("AGENT_ID", `${agent?.id}`);
    };

    // 初始化智能体列表
    const initAgentList = async () => {
      const res = await queryAgentList();
      const agentListValue = res.data || [];
      // .filter(item => {
      //   return (
      //     item.status === 1 &&
      //     (props.agentIds === undefined || props.agentIds.includes(item.id))
      //   );
      // });
      agentList.value = agentListValue;
      if (agentListValue.length > 0) {
        const agentId =
          props.initialAgentId || localStorage.getItem("AGENT_ID");
        if (agentId) {
          const agent = agentListValue.find(item => item.id === +agentId);
          updateCurrentAgent(agent || agentListValue[0]);
        } else {
          updateCurrentAgent(agentListValue[0]);
        }
      }
    };

    // 保存会话到本地存储
    const saveConversationToLocal = (conversation: ConversationDetailType) => {
      if (conversation) {
        if (conversation.chatId !== -1) {
          localStorage.setItem("CONVERSATION_ID", `${conversation.chatId}`);
        }
      } else {
        localStorage.removeItem("CONVERSATION_ID");
      }
    };

    // 选择会话
    const onSelectConversation = (
      conversation: ConversationDetailType,
      sendMsgParams?: SendMsgParamsType,
      isAdd?: boolean
    ) => {
      currentConversation.value = {
        ...conversation,
        initialMsgParams: sendMsgParams,
        isAdd,
      };
      saveConversationToLocal(conversation);

      // 通知 SusChat 组件会话已选择
      if (currentAgent.value?.chartType === "chatBI-chat") {
        susChatRef.value?.onSelectConversation(
          conversation,
          sendMsgParams,
          isAdd
        );
      }
    };

    // dify选择会话
    const onDifySelectConversation = (conversation: any) => {
      // console.log("onDifySelectConversation", conversation);
      currentDifyConversation.value = {
        ...conversation,
      };

      difyConversationId.value = conversation.id;
      difyChatRef.value?.updateConversation(conversation.id);
    };

    const onReportMsgEvent = (question: string, valid: boolean) => {
      emit("reportMsgEvent", question, valid);
    };

    // 切换历史记录可见性
    const onToggleHistoryVisible = () => {
      historyVisible.value = !historyVisible.value;
    };

    const onOpenAgents = () => {
      agentListVisible.value = !agentListVisible.value;
    };

    // 添加新会话
    const onAddConversation = () => {
      conversationRef.value?.onAddConversation();
    };

    // 选择智能体
    const onSelectAgent = (agent: ClientAgentType) => {
      if (agent.chartId === currentAgent.value?.chartId) {
        return;
      }

      updateCurrentAgent(agent);

      difyChatRef.value?.startNewConversation();
      difyConversationId.value = "";

      updateMessageContainerScroll();
    };

    // 添加新会话
    const onAddDifyConversation = () => {
      difyChatRef.value?.startNewConversation();
      difyConversationId.value = "";
    };

    // 关闭会话
    const onCloseConversation = () => {
      historyVisible.value = false;
    };

    // 生命周期钩子
    onMounted(() => {
      if (props.token) {
        setToken(props.token);
      }
      if (props.kkServerUrl) {
        setKKServerUrl(props.kkServerUrl);
      }
      if (props.kkLocalUrl) {
        setKKLocalUrl(props.kkLocalUrl);
      }
      if (!validateNull(props.isMicro)) {
        setISMICRO(props.isMicro);
      }
      initAgentList();
    });

    // 监听聊天界面可见性变化
    watch(
      () => props.chatVisible,
      val => {
        if (val) {
          nextTick(() => {
            updateMessageContainerScroll();
          });
        }
      }
    );

    // 监听当前 Dify 会话变化（简化处理，因为 DifyConversationItem 类型不包含这些属性）
    watch(
      () => currentDifyConversation.value,
      val => {
        if (!val) {
          return;
        }
        // Dify 会话的处理逻辑在 DifyChat 组件中
      }
    );

    return {
      currentConversation,
      currentDifyConversation,
      historyVisible,
      agentList,
      currentAgent,
      agentListVisible,
      conversationRef,
      difyChatRef,
      chatClass,
      onSelectConversation,
      onToggleHistoryVisible,
      onOpenAgents,
      onAddConversation,
      onSelectAgent,
      onCloseConversation,
      onDifySelectConversation,
      onUpdateConversationId,
      difyConversationId,
      onAddDifyConversation,
      susChatRef,
      onReportMsgEvent,
    };
  },
});
</script>

<style lang="less" scoped>
@import "./index.less";
</style>
